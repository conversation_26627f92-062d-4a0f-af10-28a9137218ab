{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 947505493299811221, "profile": 15657897354478470176, "path": 5837907906068023896, "deps": [[2828590642173593838, "cfg_if", false, 2937056415635414900], [3666196340704888985, "smallvec", false, 7473144789055188591], [10020888071089587331, "<PERSON>ap<PERSON>", false, 13935194552428359985], [14196108479452351812, "instant", false, 8215391684703788326], [14814334185036658946, "build_script_build", false, 4437786898635473563]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot_core-253c1943374a3aa3\\dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}