[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "dash-influxdb-app"
version = "0.1.0"
description = "Dash application with InfluxDB integration"
requires-python = ">=3.11"
dependencies = [
    "dash==3.1.1",
    "influxdb-client==1.49.0",
    "pandas==2.3.1",
    "plotly==6.2.0",
    "python-dotenv==1.1.1",
    "prettytable==3.16.0",
    "flask<3.1",
    "redis==5.0.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.1",
    "black>=25.1.0",
]

[tool.setuptools]
packages = [] 


