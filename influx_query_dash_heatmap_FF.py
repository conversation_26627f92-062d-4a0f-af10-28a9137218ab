import os
from dotenv import load_dotenv
from influxdb_client import InfluxDBClient
import pandas as pd
import dash
from dash import dcc, html, Input, Output, State
from dash.dependencies import ALL
import plotly.graph_objs as go
from datetime import datetime, timedelta, timezone
import threading
import json
from analytics import get_custom_html
import time

# 导入Redis缓存模块
from redis_cache import (
    get_base_data_cache, set_base_data_cache,
    get_latest_data_cache, set_latest_data_cache,
    combine_cached_data, should_refresh_base_cache,
    get_cache_stats, clear_cache
)

# 加载环境变量
load_dotenv()

# InfluxDB 连接设置
url = os.getenv('INFLUXDB_URL')
token = os.getenv('INFLUXDB_TOKEN')
org = os.getenv('INFLUXDB_ORG')
bucket = os.getenv('INFLUXDB_BUCKET')

# 刷新间隔设置（从环境变量读取，默认22秒）
REFRESH_INTERVAL_SECONDS = int(os.getenv('REFRESH_INTERVAL_SECONDS', '22'))

# 初始化 InfluxDB 客户端（全局变量）
client = None
client_lock = threading.RLock()  # 保护客户端重连的锁

def get_influx_client():
    """获取InfluxDB客户端，支持自动重连"""
    global client
    with client_lock:
        try:
            if client is None:
                print("初始化InfluxDB客户端...")
                client = InfluxDBClient(url=url, token=token, org=org, timeout=30_000)

            # 简单的健康检查 - 尝试获取组织信息
            client.organizations_api().find_organizations()
            return client

        except Exception as e:
            print(f"InfluxDB连接异常，尝试重连: {e}")
            try:
                client = InfluxDBClient(url=url, token=token, org=org, timeout=30_000)
                print("InfluxDB重连成功")
                return client
            except Exception as reconnect_error:
                print(f"InfluxDB重连失败: {reconnect_error}")
                client = None
                raise

# 初始化客户端
try:
    client = get_influx_client()
    print("InfluxDB客户端初始化成功")
except Exception as e:
    print(f"InfluxDB客户端初始化失败: {e}")
    client = None

# 初始化 Dash 应用，并设置标题，禁用自动标题更新以避免浏览器标签页闪烁
app = dash.Dash(__name__, suppress_callback_exceptions=True, title='全市场仓位监控', update_title=None)

# 使用新的模块设置自定义HTML布局
app.index_string = get_custom_html()

# 使用全局变量来跟踪锁状态
query_lock = threading.RLock()
query_is_locked = False
query_timestamp = 0
QUERY_TIMEOUT = 30  # 减少到30秒超时
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数

# 定义时间间隔选项
time_intervals = [
    {'label': '30秒', 'value': 30},
    {'label': '1分钟', 'value': 60},
    {'label': '2分钟', 'value': 120},
    {'label': '3分钟', 'value': 180},
    {'label': '4分钟', 'value': 240},
    {'label': '5分钟', 'value': 300},
    {'label': '15分钟', 'value': 900},
    {'label': '30分钟', 'value': 1800},
]

# 修改应用布局
app.layout = html.Div([
    dcc.Store(id='initial-data', storage_type='memory'),
    html.Div([
        # 左侧：时间间隔按钮区域
        html.Div([
            html.Div([
                html.Button(interval['label'], id={'type': 'interval-btn', 'index': interval['value']}, n_clicks=0,
                            style={
                                'margin': '2px',
                                'padding': '3px 8px',
                                'fontSize': '12px',
                                'borderRadius': '4px',
                                # Safari兼容性样式
                                'WebkitAppearance': 'none',
                                'WebkitTapHighlightColor': 'transparent',
                                'userSelect': 'none',
                                'cursor': 'pointer',
                                'outline': 'none'
                            })
                for interval in time_intervals
            ], style={'display': 'inline-block'}),
        ], style={
            'display': 'flex',
            'alignItems': 'center',
            'marginLeft': '20px'  # 左边留白
        }),

        # 中间：倒计时和暂停更新
        html.Div([
            html.Div(id='countdown-display', children="⏰--s",
                    style={
                        'display': 'inline-block',
                        'marginRight': '12px',
                        'fontSize': '12px',
                        'color': '#f0f0f0'  # 灰色文字
                    }),
            dcc.Checklist(
                id='pause-toggle-checklist',
                options=[{'label': '暂停更新', 'value': 'paused'}],
                value=[],
                style={
                    'display': 'inline-block',
                    'fontSize': '11px',  # 与持仓阈值标签字体大小一致
                    'color': '#f0f0f0'  # 与持仓阈值标签颜色一致
                }
            ),
        ], style={
            'display': 'flex',
            'alignItems': 'center'
        }),

        # 右侧：设置项目（更紧凑）
        html.Div([
            html.Div([
                html.Label('持仓阈值%: ', style={
                    'marginRight': '4px',
                    'fontSize': '11px',
                    'color': '#FCC623'  # 浅黄色文字，在浅色和深色主题下都清晰可见
                }),
                dcc.Input(id='alert-threshold', type='number', value=1.2, min=0, max=100, step=0.1,
                          style={
                              'width': '50px',  # 稍微增加宽度以容纳小数点
                              'height': '22px',
                              'fontSize': '11px',
                              'padding': '1px 3px',
                              'backgroundColor': '#343a40',  # 更深的灰色背景
                              'border': '1px solid #495057',  # 更深的灰色边框
                              'color': '#f8f9fa'  # 更深的灰色文字
                          }),
            ], style={'display': 'inline-block', 'marginRight': '8px'}),
            # 现代化静音按钮
            html.Button(
                id='mute-toggle-button',
                children='🔊',  # 默认非静音状态
                n_clicks=0,
                style={
                    'width': '28px',
                    'height': '28px',
                    'borderRadius': '50%',
                    'border': 'none',
                    'backgroundColor': '#20c997',  # 绿色背景（非静音）
                    'color': 'white',
                    'fontSize': '14px',
                    'cursor': 'pointer',
                    'display': 'inline-flex',
                    'alignItems': 'center',
                    'justifyContent': 'center',
                    'marginRight': '6px',
                    'transition': 'all 0.2s ease',
                    'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'
                },
                title='点击切换静音状态'
            ),
            # 主题切换按钮
            html.Button(
                id='theme-toggle-button',
                children='🌙',  # 默认显示月亮图标（当前是浅色主题）
                n_clicks=0,
                style={
                    'width': '28px',
                    'height': '28px',
                    'borderRadius': '50%',
                    'border': 'none',
                    'backgroundColor': '#6f42c1',  # 紫色背景
                    'color': 'white',
                    'fontSize': '14px',
                    'cursor': 'pointer',
                    'display': 'inline-flex',
                    'alignItems': 'center',
                    'justifyContent': 'center',
                    'marginRight': '6px',
                    'transition': 'all 0.2s ease',
                    'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'
                },
                title='切换到深色主题'
            ),
        ], style={
            'display': 'flex',
            'alignItems': 'center',
            'marginRight': '20px'  # 右边留白
        })
    ], id='control-bar', style={
        'display': 'flex',
        'justifyContent': 'space-between',
        'alignItems': 'center',
        'margin': '5px 0',  # 去掉左右边距
        'padding': '6px 0',    # 稍微增加内边距
        'borderBottom': '1px solid #444444',  # 更深的底部分隔线
        'backgroundColor': '#2c2c2c'  # 更深的灰色背景，替代白色
    }),

    # 热力图容器（使用annotations显示右侧标签）
    html.Div([
        dcc.Graph(
            id='heatmap',
            style={'width': '100%'},
            config={
                'displayModeBar': False,  # 隐藏工具栏
                'staticPlot': False,      # 允许交互
                'scrollZoom': False,      # 禁用滚轮缩放
                'doubleClick': 'reset',   # 双击重置
                'showTips': False,        # 隐藏提示
                'displaylogo': False      # 隐藏plotly logo
            }
        )
    ], id='heatmap-container', style={'backgroundColor': '#1e1e1e', 'minHeight': '90vh'}),

    # 其他组件
    dcc.Interval(
        id='interval-component',
        interval=REFRESH_INTERVAL_SECONDS*1000,  # 初始刷新间隔，会根据时间周期动态调整
        n_intervals=0
    ),
    dcc.Store(id='selected-interval', data=30),  # 存储选中的时间间隔，默认为30秒
    dcc.Store(id='is-paused-store', data=False), # 存储暂停状态
    dcc.Store(id='mute-state', data=False),  # 存储静音状态，False=非静音，True=静音
    dcc.Store(id='theme-state', data='dark'),  # 存储主题状态，'dark'=深色主题，'light'=浅色主题
    html.Audio(id='audio-player', src=app.get_asset_url('alert.mp3'), style={'display': 'none'}),
    dcc.Store(id='play-count', data=0),
    html.Div(id='audio-play-trigger', style={'display': 'none'}),
    dcc.Store(id='highlighted-symbol', data=None),  # 存储被点击的symbol



])

# 优化的查询数据函数（支持Redis缓存）
def query_data_optimized(interval_seconds):
    """优化的数据查询策略，使用Redis分层缓存"""
    current_time = datetime.now(timezone.utc)

    try:
        # 1. 尝试获取缓存数据
        base_df = get_base_data_cache(interval_seconds)
        latest_df = get_latest_data_cache(interval_seconds)

        # 2. 如果有完整缓存，直接使用
        if base_df is not None and latest_df is not None:
            print("🚀 使用完整缓存数据")
            combined_df = combine_cached_data(base_df, latest_df)
            return process_data_for_interval(combined_df, interval_seconds)

        # 3. 如果只有基础缓存，只查询最新数据
        if base_df is not None and not should_refresh_base_cache(interval_seconds):
            print("🔄 使用基础缓存 + 查询增量数据")
            latest_data = query_latest_data_only(current_time, interval_seconds)
            if not latest_data.empty:
                set_latest_data_cache(latest_data, interval_seconds)
                combined_df = combine_cached_data(base_df, latest_data)
                return process_data_for_interval(combined_df, interval_seconds)

        # 4. 无缓存或需要刷新，执行完整查询
        print("📊 执行完整数据查询")
        full_df = query_full_data(interval_seconds)
        if not full_df.empty:
            # 分离基础数据和最新数据
            cutoff_time = current_time - timedelta(minutes=3)  # 最近3分钟作为增量数据
            base_data = full_df[full_df['time'] < cutoff_time]
            latest_data = full_df[full_df['time'] >= cutoff_time]

            # 缓存数据
            if not base_data.empty:
                set_base_data_cache(base_data, interval_seconds)
            if not latest_data.empty:
                set_latest_data_cache(latest_data, interval_seconds)

        return full_df

    except Exception as e:
        print(f"缓存查询失败，降级到原始查询: {e}")
        return query_data_fallback(interval_seconds)

def query_latest_data_only(current_time, interval_seconds, minutes_back=3):
    """只查询最近几分钟的数据"""
    try:
        current_client = get_influx_client()
        if current_client is None:
            raise Exception("无法获取InfluxDB客户端连接")

        start_time = (current_time - timedelta(minutes=minutes_back)).strftime("%Y-%m-%dT%H:%M:%SZ")

        query = f'''
        import "strings"
        from(bucket:"{bucket}")
            |> range(start: {start_time})
            |> filter(fn: (r) => r._measurement == "open_interest")
            |> window(every: {interval_seconds}s)
            |> last()
            |> filter(fn: (r) => not strings.hasSuffix(v: r.symbol, suffix: "USDC"))
            |> group(columns: ["symbol"])
            |> sort(columns: ["_time"])
        '''

        print(f"🔍 查询增量数据: 最近{minutes_back}分钟")
        result = current_client.query_api().query(query=query, org=org)

        # 处理查询结果
        records = []
        for table in result:
            for record in table.records:
                records.append({
                    'time': record.get_time(),
                    'symbol': record.values.get('symbol'),
                    'value': float(record.get_value()) if record.get_value() is not None else 0.0
                })

        df = pd.DataFrame(records)
        if not df.empty:
            df = df.sort_values(['symbol', 'time'])
            df['pct_change'] = df.groupby('symbol')['value'].pct_change() * 100
            df['pct_change'] = df['pct_change'].round(1)
            df['time'] = df['time'].apply(lambda x: x.floor(f'{interval_seconds}s'))
            df = df[['time', 'symbol', 'pct_change']]
            print(f"✅ 增量查询成功: {df.shape}")

        return df

    except Exception as e:
        print(f"查询增量数据失败: {e}")
        return pd.DataFrame()

def query_full_data(interval_seconds):
    """执行完整的数据查询（原始逻辑）"""
    return query_data_fallback(interval_seconds)

def process_data_for_interval(df, interval_seconds):
    """处理数据以适应指定的时间间隔"""
    if df.empty:
        return df

    try:
        # 选择最近24个时间点和前30个最活跃的交易对
        last_24_times = df['time'].unique()[-24:]
        if len(last_24_times) > 0:
            recent_df = df[df['time'].isin(last_24_times)]
            top_symbols = recent_df.groupby('symbol')['pct_change'].apply(
                lambda x: abs(x).sum()
            ).nlargest(30).index.tolist()

            df = df[df['symbol'].isin(top_symbols)]

        df = df[['time', 'symbol', 'pct_change']]
        print(f"✅ 数据处理完成: {df.shape}")
        return df

    except Exception as e:
        print(f"数据处理失败: {e}")
        return df

# 原始查询数据函数（作为降级方案）
def query_data_fallback(interval_seconds):
    """查询InfluxDB数据，支持自动重连和重试机制（降级方案）"""
    retry_count = 0
    last_error = None

    while retry_count < MAX_RETRY_ATTEMPTS:
        try:
            # 获取健康的客户端连接
            current_client = get_influx_client()
            if current_client is None:
                raise Exception("无法获取InfluxDB客户端连接")

            # 根据时间周期计算查询范围：26个周期的数据（显示24个，留2个余量）
            total_seconds = 26 * interval_seconds
            start_time = (datetime.now(timezone.utc) - timedelta(seconds=total_seconds)).strftime("%Y-%m-%dT%H:%M:%SZ")

            query = f'''
            import "strings"
            from(bucket:"{bucket}")
                |> range(start: {start_time})
                |> filter(fn: (r) => r._measurement == "open_interest")
                |> window(every: {interval_seconds}s)
                |> last()
                |> filter(fn: (r) => not strings.hasSuffix(v: r.symbol, suffix: "USDC"))
                |> group(columns: ["symbol"])
                |> sort(columns: ["_time"])
            '''
            print(f"执行查询 (尝试 {retry_count + 1}/{MAX_RETRY_ATTEMPTS}): {query}")

            # 执行查询并获取结果
            result = current_client.query_api().query(query=query, org=org)

            # 处理查询结果
            records = []
            for table in result:
                for record in table.records:
                    records.append({
                        'time': record.get_time(),
                        'symbol': record.values.get('symbol'),
                        'value': float(record.get_value()) if record.get_value() is not None else 0.0
                    })

            # 转换为 DataFrame 并进行数据处理
            df = pd.DataFrame(records)
            if df.empty:
                print("查询返回空数据")
                return df

            df = df.sort_values(['symbol', 'time'])
            df['pct_change'] = df.groupby('symbol')['value'].pct_change() * 100
            df['pct_change'] = df['pct_change'].round(1)

            # 使用 timedelta 来向下取整时间
            df['time'] = df['time'].apply(lambda x: x.floor(f'{interval_seconds}s'))

            # 选择最近24个时间点和前30个最活跃的交易对
            last_24_times = df['time'].unique()[-24:]
            top_symbols = df[df['time'].isin(last_24_times)].groupby('symbol')['pct_change'].apply(lambda x: abs(x).sum()).nlargest(30).index.tolist()

            df = df[df['symbol'].isin(top_symbols)]
            df = df[['time', 'symbol', 'pct_change']]

            print(f"\n查询成功返回 {len(df)} 条记录")
            print(f"数据形状: {df.shape}")
            print("\n处理后数据的前几行:")
            print(df.head(10))

            return df

        except Exception as e:
            last_error = e
            retry_count += 1
            print(f"查询数据时出错 (尝试 {retry_count}/{MAX_RETRY_ATTEMPTS}): {e}")

            if retry_count < MAX_RETRY_ATTEMPTS:
                print(f"等待 {retry_count * 2} 秒后重试...")
                time.sleep(retry_count * 2)  # 递增延迟
                # 强制重置客户端连接
                global client
                with client_lock:
                    client = None
            else:
                print(f"查询失败，已达到最大重试次数: {last_error}")
                break

    # 所有重试都失败了，返回空DataFrame
    return pd.DataFrame()

# 主查询函数（优先使用缓存）
def query_data(interval_seconds):
    """主查询函数，优先使用Redis缓存，失败时降级到原始查询"""
    try:
        # 尝试使用优化的缓存查询
        return query_data_optimized(interval_seconds)
    except Exception as e:
        print(f"缓存查询异常，使用降级方案: {e}")
        return query_data_fallback(interval_seconds)

def create_error_figure(error_message):
    """创建显示错误信息的图表"""
    fig = go.Figure()
    fig.add_annotation(
        text=f"⚠️ 连接异常<br><br>{error_message}<br><br>请稍等片刻或手动刷新页面",
        xref="paper", yref="paper",
        x=0.5, y=0.5,
        showarrow=False,
        font=dict(size=16, color="#F6465D", family='"Binance PLEX Regular", "Noto Sans SC"'),
        bgcolor="rgba(246, 70, 93, 0.1)",
        bordercolor="#F6465D",
        borderwidth=2,
        borderpad=20
    )
    fig.update_layout(
        paper_bgcolor='#1e1e1e',
        plot_bgcolor='#1e1e1e',
        height=400,
        margin=dict(l=50, r=50, t=50, b=50)
    )
    return fig

# 添加初始数据加载回调
@app.callback(
    Output('initial-data', 'data'),
    Input('interval-component', 'n_intervals')
)
def load_initial_data(n):
    if n == 0:  # 仅在页面首次加载时执行
        df = query_data(30)  # 默认使用30秒间隔
        return df.to_json(date_format='iso', orient='split')
    return dash.no_update

# 修改更新热力图回调，添加更多的错误处理和日志记录
@app.callback(
    [Output('heatmap', 'figure'),
     Output('play-count', 'data')],
    [Input('interval-component', 'n_intervals'),
     Input('selected-interval', 'data'),
     Input('alert-threshold', 'value'),
     Input('initial-data', 'data'),
     Input('highlighted-symbol', 'data'),
     Input('theme-state', 'data')],
    [State('is-paused-store', 'data')],
    prevent_initial_call='initial_duplicate'
)
def update_heatmap(n, selected_interval, alert_threshold, initial_data, highlighted_symbol, theme_state, is_paused):
    global query_timestamp, query_is_locked

    try:
        ctx = dash.callback_context
        triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]

        print(f"触发回调 update_heatmap: triggered_id={triggered_id}, n={n}, selected_interval={selected_interval}")

        df_to_process = None

        if triggered_id == 'initial-data' and initial_data is not None:
            try:
                df_to_process = pd.read_json(initial_data, orient='split')
                print(f"从 initial-data 读取数据: {df_to_process.shape}")
            except Exception as e:
                print(f"处理 initial-data 时出错: {e}")
                return dash.no_update, dash.no_update

        if df_to_process is None and triggered_id != 'initial-data':
            current_time = time.time()
            if query_is_locked:
                if current_time - query_timestamp > QUERY_TIMEOUT:
                    print(f"查询超时（{QUERY_TIMEOUT}秒），强制释放锁")
                    query_is_locked = False
                else:
                    print("上一次查询还未完成，跳过本次更新")
                    return dash.no_update, dash.no_update

            acquired = False
            try:
                acquired = query_lock.acquire(blocking=False)
                if acquired:
                    query_is_locked = True
                    query_timestamp = current_time
                    print(f"更新热力图，选择的间隔: {selected_interval}秒")

                    # 检查是否暂停更新
                    if is_paused:
                        print("更新已暂停，跳过查询")
                        query_is_locked = False
                        query_lock.release()
                        return dash.no_update, dash.no_update

                    df_query_result = query_data(selected_interval)
                    if df_query_result is None or df_query_result.empty:
                        print("查询返回空数据，显示连接错误状态")
                        query_is_locked = False
                        query_lock.release()
                        # 返回错误状态图表而不是no_update
                        error_fig = create_error_figure("InfluxDB连接断开或无数据")
                        return error_fig, 0
                    else:
                        df_to_process = df_query_result
                else:
                    print("无法获取查询锁，可能有其他查询正在进行")
                    return dash.no_update, dash.no_update
            except Exception as e:
                print(f"查询数据时出错: {e}")
                if acquired:
                    query_is_locked = False
                    query_lock.release()
                # 返回错误状态图表
                error_fig = create_error_figure(f"查询错误: {str(e)[:100]}")
                return error_fig, 0
            finally:
                if acquired:
                    query_is_locked = False
                    query_lock.release()

        if df_to_process is None or df_to_process.empty:
            print("没有有效数据可供处理")
            error_fig = create_error_figure("暂无数据或数据加载中")
            return error_fig, 0

        df = df_to_process

        try:
            df['time'] = df['time'] + timedelta(hours=8)
            pivot_table = df.pivot(index='symbol', columns='time', values='pct_change')

            print(f"透视表形状: {pivot_table.shape}")

            if pivot_table.empty or pivot_table.shape[1] == 0:
                print("透视表为空或没有列")
                error_fig = create_error_figure("数据处理异常：透视表为空")
                return error_fig, 0

            last_cols = min(24, pivot_table.shape[1])
            if last_cols == 0:
                print("没有可用的列")
                error_fig = create_error_figure("数据处理异常：没有可用的时间列")
                return error_fig, 0

            last_times = pivot_table.columns[-last_cols:]
            pivot_table = pivot_table[last_times]

            sort_cols = min(3, pivot_table.shape[1])
            if sort_cols == 0:
                print("没有可用的排序列")
                error_fig = create_error_figure("数据处理异常：没有可用的排序列")
                return error_fig, 0

            last_sort_times = pivot_table.columns[-sort_cols:]
            max_abs_change = pivot_table[last_sort_times].abs().sum(axis=1)
            sorted_symbols = max_abs_change.sort_values(ascending=False).index
            pivot_table = pivot_table.loc[sorted_symbols]

            # 保存原始符号并创建不带USDT的显示版本
            original_symbols = pivot_table.index.tolist()
            display_symbols = [s.replace('USDT', '') for s in original_symbols]

            # 创建带有HTML标记的文本数组，大于0.5的数值使用粗体
            text_with_html = []
            for row in pivot_table.values:
                text_row = []
                for val in row:
                    if pd.isna(val):
                        text_row.append('')
                    elif abs(val) > 0.5:
                        text_row.append(f'<b>{val:.1f}</b>')
                    else:
                        text_row.append(f'{val:.1f}')
                text_with_html.append(text_row)

            # 根据主题选择配色方案
            if theme_state == 'light':
                colorscale = [
                    [0, '#F6465D'],             # 浅色主题红色（更新为用户指定）
                    [0.5, '#E8E8E8'],           # 浅色主题背景色（用户指定的柔和灰色）
                    [1, '#2EBD85']              # 浅色主题绿色（用户指定）
                ]
                text_color = "#1a1d21"  # 浅色主题文字颜色（调暗）
                hover_bg = "rgba(240,241,243,0.9)"  # 浅色主题悬停背景（调暗30%）
                hover_border = "#5a6169"  # 浅色主题悬停边框（调暗）
                hover_text = "#1a1d21"  # 浅色主题悬停文字（调暗）
            else:
                colorscale = [
                    [0, '#F6465D'],             # 深色主题红色（更新为用户指定）
                    [0.5, '#1e1e1e'],           # 深色主题背景色（使用深色背景）
                    [1, '#2EBD85']              # 深色主题绿色（用户指定）
                ]
                text_color = "#f0f0f0"  # 深色主题文字颜色
                hover_bg = "rgba(64,64,64,0.5)"  # 深色主题悬停背景
                hover_border = "#adb5bd"  # 深色主题悬停边框
                hover_text = "white"  # 深色主题悬停文字

            # 创建单一热力图，不使用subplot
            heatmap = go.Heatmap(
                z=pivot_table.values,
                x=pivot_table.columns,
                y=display_symbols, # 使用显示符号作为Y轴
                colorscale=colorscale,
                zmin=-2.1,
                zmax=2.1,
                zmid=0,
                text=text_with_html,
                texttemplate='%{text}',
                textfont={
                    "size": 10,
                    "color": text_color,
                    "family": '"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif'
                },
                hovertemplate='时间: <b>%{x}</b><br>' +
                            '交易对: <b>%{y}USDT</b><br>' +
                            '变化: <b>%{z:.1f}%</b><extra></extra>',
                hoverlabel=dict(
                    bgcolor=hover_bg,
                    bordercolor=hover_border,
                    font_size=12,
                    font_color=hover_text,
                    font_family='"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif'
                ),
                xgap=1,  # 添加水平分隔线
                ygap=1,  # 添加垂直分隔线
                showscale=True,  # 显示颜色刻度
                colorbar=dict(
                    title='变化 (%)',
                    tickvals=[-2.1, -1.4, -0.7, 0, 0.7, 1.4, 2.1],
                    ticktext=['-2.1', '-1.4', '-0.7', '0', '0.7', '1.4', '2.1'],
                    tickfont={
                        "size": 8,
                        "family": '"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
                        "color": text_color
                    },
                    x=0.5,
                    y=-0.10,
                    xanchor='center',
                    yanchor='bottom',
                    orientation='h',
                    thickness=5,
                    len=1.0
                ),
                # 设置固定的格子尺寸
                dx=1,  # X方向格子宽度
                dy=1,   # Y方向格子高度，确保每行高度一致
                # 确保热力图可以被点击
                name='heatmap',
                # 添加行高亮效果配置
                hoverongaps=False,  # 不在间隙上悬停
                # 使用自定义CSS样式来实现行高亮
                customdata=display_symbols  # 传递symbol数据
            )

            fig = go.Figure(data=heatmap)

            # 计算高亮行索引（仅用于1像素横线高亮）
            highlighted_row_index = None
            if highlighted_symbol and highlighted_symbol in display_symbols:
                highlighted_row_index = display_symbols.index(highlighted_symbol)

            if selected_interval < 60:  # 小于1分钟
                time_format = '%H:%M:%S'
            elif selected_interval < 3600:  # 小于1小时
                time_format = '%H:%M'
            else:  # 1小时或更长
                time_format = '%m-%d %H:%M'

            # 计算固定的图表高度以确保行对齐
            num_symbols = len(display_symbols)
            cell_height = 30  # 每个格子的固定高度（像素）
            top_margin = 50
            bottom_margin = 80
            chart_height = num_symbols * cell_height + top_margin + bottom_margin

            # 根据主题设置链接颜色
            if theme_state == 'light':
                link_color = '#ffc107'  # 浅色主题使用稍深的黄色
            else:
                link_color = '#F0B90B'  # 深色主题使用原来的黄色

            # 添加右侧symbol标签作为annotations
            annotations = []

            for i, (original_symbol, display_symbol) in enumerate(zip(original_symbols, display_symbols)):
                # 添加symbol文本（使用font设置粗体，与左侧Y轴保持一致）
                annotations.append(dict(
                    x=1.02,  # 在图表右侧
                    y=i,     # Y位置对应热力图的行
                    xref='paper',  # 相对于整个图表区域
                    yref='y',      # 相对于Y轴数据坐标
                    text=f'<a href="https://www.binance.com/zh-CN/futures/{original_symbol}?cl=public" target="_blank" style="color:{link_color}; text-decoration:none;">↗</a>  {display_symbol}  <a href="https://www.coinglass.com/tv/zh/Binance_{original_symbol}" target="_blank" style="color:{link_color}; text-decoration:none;">↗</a>',
                    showarrow=False,
                    font=dict(
                        size=11,
                        family='"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
                        color=text_color,
                        weight='bold'  # 添加粗体设置，与左侧Y轴保持一致
                    ),
                    xanchor='left',
                    yanchor='middle'
                ))

            # 根据主题设置网格和背景颜色
            if theme_state == 'light':
                grid_color = '#dee2e6'
                bg_color = '#E8E8E8'  # 浅色主题使用柔和灰色背景
            else:
                grid_color = '#444444'
                bg_color = '#1e1e1e'  # 深色主题保持原来的深色背景

            # 更新布局
            fig.update_layout(
                xaxis_title=None,
                yaxis_title=None,
                hovermode='y',  # 添加原生的行悬停高亮效果
                font=dict(
                    family='"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
                    size=12,
                    color=text_color
                ),
                yaxis=dict(
                    autorange='reversed',
                    side='left',
                    title=dict(standoff=10),
                    tickmode='array',
                    ticktext=display_symbols,
                    tickvals=list(range(len(display_symbols))),
                    tickfont=dict(
                        size=11,
                        family='"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
                        color=text_color,
                        weight='bold'
                    ),
                    gridcolor=grid_color,
                    fixedrange=True,
                    range=[-0.5, num_symbols - 0.5],
                    dtick=1,
                    showgrid=False,
                    gridwidth=1,
                ),
                xaxis=dict(
                    tickmode='array',
                    tickvals=pivot_table.columns,
                    ticktext=[ts.strftime(time_format) for ts in pivot_table.columns],
                    tickangle=45,
                    title=dict(standoff=10),
                    tickfont=dict(
                        family='"Binance PLEX Regular", "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
                        color=text_color
                    ),
                    gridcolor=grid_color,
                    showgrid=False
                ),
                margin=dict(l=50, r=120, t=top_margin, b=bottom_margin),  # 增加右边距为annotations留空间
                height=chart_height,
                width=None,
                autosize=False,
                paper_bgcolor=bg_color,
                plot_bgcolor=bg_color,
                annotations=annotations  # 添加annotations
            )

            # 预先为每一行创建下划线shape，通过可见性控制高亮
            # 这样可以避免每次点击都重新创建shape，提高响应速度
            for i in range(len(display_symbols)):
                is_visible = (highlighted_row_index is not None and i == highlighted_row_index)
                fig.add_shape(
                    type="line",
                    x0=0,  # 从图表左边开始
                    x1=1,  # 到图表右边结束
                    y0=i + 0.5,  # 行的下边界
                    y1=i + 0.5,  # 水平线
                    xref="paper",  # 使用paper坐标系统，覆盖整个图表宽度
                    yref="y",      # 使用y轴数据坐标
                    line=dict(color=link_color, width=1),  # 使用主题适配的链接颜色
                    layer="above",
                    visible=is_visible  # 只有被选中的行才可见
                )



            try:
                latest_column = pivot_table.iloc[:, -1]
                alerts = (latest_column.abs() >= alert_threshold).sum()
            except IndexError:
                print("无法获取最新列，可能没有足够的数据")
                alerts = 0

            print("更新成功")
            return fig, alerts

        except Exception as e:
            print(f"处理数据或创建热力图时出错: {e}")
            import traceback
            traceback.print_exc()
            error_fig = create_error_figure(f"数据处理错误: {str(e)[:100]}")
            return error_fig, 0

    except Exception as e:
        print(f"更新热力图回调函数发生未处理异常: {e}")
        import traceback
        traceback.print_exc()
        error_fig = create_error_figure(f"系统异常: {str(e)[:100]}")
        return error_fig, 0

# 客户端回调，用于主题切换
app.clientside_callback(
    """
    function(theme) {
        console.log("Switching theme to:", theme);
        var body = document.body;
        if (theme === 'light') {
            body.classList.add('light-theme');
            // 保存主题到localStorage
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.remove('light-theme');
            // 保存主题到localStorage
            localStorage.setItem('theme', 'dark');
        }
        return null;
    }
    """,
    Output('audio-play-trigger', 'children', allow_duplicate=True),
    Input('theme-state', 'data'),
    prevent_initial_call=True
)

# 客户端回调，用于控制音频播放
app.clientside_callback(
    """
    function(playCount, isMuted) {
        console.log("Attempting to play audio", playCount, "times. Mute:", isMuted);
        if (playCount > 0 && !isMuted) {
            var audio = document.getElementById('audio-player');
            if (!audio) {
                console.error("Audio element not found");
                return null;
            }
            var playPromise;
            var playNextAudio = function() {
                if (playCount > 0) {
                    playCount--;
                    console.log("Playing audio, remaining plays:", playCount);
                    playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.then(_ => {
                            audio.addEventListener('ended', playNextAudio, {once: true});
                        }).catch(error => {
                            console.error('播放出错:', error);
                        });
                    }
                }
            };
            playNextAudio();
        } else {
            console.log("Not playing audio due to mute or play count");
        }
        return null;
    }
    """,
    Output('audio-play-trigger', 'children'),
    Input('play-count', 'data'),
    State('mute-state', 'data')
)



# 主题切换按钮点击回调
@app.callback(
    Output('theme-state', 'data'),
    Input('theme-toggle-button', 'n_clicks'),
    State('theme-state', 'data'),
    prevent_initial_call=True
)
def toggle_theme_state(n_clicks, current_theme):
    if n_clicks > 0:
        return 'light' if current_theme == 'dark' else 'dark'
    return current_theme

# 静音按钮点击回调 - 切换静音状态并在取消静音时播放声音
@app.callback(
    [Output('mute-state', 'data'),
     Output('play-count', 'data', allow_duplicate=True)],
    Input('mute-toggle-button', 'n_clicks'),
    State('mute-state', 'data'),
    prevent_initial_call=True
)
def toggle_mute_state(n_clicks, current_mute_state):
    if n_clicks > 0:
        new_mute_state = not current_mute_state
        # 如果从静音切换到非静音，播放一次声音作为反馈
        if current_mute_state and not new_mute_state:
            return new_mute_state, 1  # 播放一次声音
        else:
            return new_mute_state, dash.no_update
    return current_mute_state, dash.no_update

# 主题切换按钮样式更新回调
@app.callback(
    [Output('theme-toggle-button', 'children'),
     Output('theme-toggle-button', 'style'),
     Output('theme-toggle-button', 'title')],
    Input('theme-state', 'data')
)
def update_theme_button_style(current_theme):
    if current_theme == 'dark':
        # 深色主题：显示太阳图标，表示点击切换到浅色主题
        return '☀️', {
            'width': '28px',
            'height': '28px',
            'borderRadius': '50%',
            'border': 'none',
            'backgroundColor': '#ffc107',  # 黄色背景
            'color': 'white',
            'fontSize': '14px',
            'cursor': 'pointer',
            'display': 'inline-flex',
            'alignItems': 'center',
            'justifyContent': 'center',
            'marginRight': '6px',
            'transition': 'all 0.2s ease',
            'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
            'transform': 'scale(1)'
        }, '切换到浅色主题'
    else:
        # 浅色主题：显示月亮图标，表示点击切换到深色主题
        return '🌙', {
            'width': '28px',
            'height': '28px',
            'borderRadius': '50%',
            'border': 'none',
            'backgroundColor': '#6f42c1',  # 紫色背景
            'color': 'white',
            'fontSize': '14px',
            'cursor': 'pointer',
            'display': 'inline-flex',
            'alignItems': 'center',
            'justifyContent': 'center',
            'marginRight': '6px',
            'transition': 'all 0.2s ease',
            'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
            'transform': 'scale(1)'
        }, '切换到深色主题'

# 静音按钮样式更新回调
@app.callback(
    [Output('mute-toggle-button', 'children'),
     Output('mute-toggle-button', 'style'),
     Output('mute-toggle-button', 'title')],
    Input('mute-state', 'data')
)
def update_mute_button_style(is_muted):
    if is_muted:
        # 静音状态：红色背景，静音图标
        return '🔇', {
            'width': '28px',
            'height': '28px',
            'borderRadius': '50%',
            'border': 'none',
            'backgroundColor': '#dc3545',  # 红色背景（静音）
            'color': 'white',
            'fontSize': '14px',
            'cursor': 'pointer',
            'display': 'inline-flex',
            'alignItems': 'center',
            'justifyContent': 'center',
            'marginRight': '6px',
            'transition': 'all 0.2s ease',
            'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
            'transform': 'scale(1)'
        }, '点击取消静音'
    else:
        # 非静音状态：绿色背景，音量图标
        return '🔊', {
            'width': '28px',
            'height': '28px',
            'borderRadius': '50%',
            'border': 'none',
            'backgroundColor': '#20c997',  # 绿色背景（非静音）
            'color': 'white',
            'fontSize': '14px',
            'cursor': 'pointer',
            'display': 'inline-flex',
            'alignItems': 'center',
            'justifyContent': 'center',
            'marginRight': '6px',
            'transition': 'all 0.2s ease',
            'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
            'transform': 'scale(1)'
        }, '点击静音'

# 使用客户端回调格式化持仓阈值输入框显示 - 保持一位小数
app.clientside_callback(
    """
    function(value) {
        if (value !== null && value !== undefined) {
            try {
                var numValue = parseFloat(value);
                if (isNaN(numValue)) {
                    return 1.5;
                }
                // 确保值在合理范围内并格式化为一位小数
                var formattedValue = Math.max(0, Math.min(100, numValue));
                return Math.round(formattedValue * 10) / 10;  // 保留一位小数
            } catch (e) {
                return 2.0;
            }
        }
        return 1.5;
    }
    """,
    Output('alert-threshold', 'value'),
    Input('alert-threshold', 'value'),
    prevent_initial_call=False  # 允许初始调用
)

# 处理热力图点击事件
@app.callback(
    Output('highlighted-symbol', 'data'),
    Input('heatmap', 'clickData'),
    prevent_initial_call=True
)
def handle_heatmap_click(clickData):
    if clickData is None:
        return None

    # 从clickData中提取被点击的symbol（y值）
    clicked_symbol = clickData['points'][0]['y']
    print(f"点击了symbol: {clicked_symbol}")
    return clicked_symbol

# 添加新的回调函数来更新按钮样式（支持主题切换）
@app.callback(
    Output({'type': 'interval-btn', 'index': ALL}, 'style'),
    [Input('selected-interval', 'data'),
     Input('theme-state', 'data')],
    prevent_initial_call=False  # 确保页面加载时就触发
)
def update_button_style(selected_interval, theme_state):
    if theme_state == 'light':
        # 浅色主题样式
        selected_style = {
            'margin': '2px',
            'padding': '3px 8px',
            'fontSize': '12px',
            'backgroundColor': '#0d6efd',  # 蓝色背景表示选中（浅色主题）
            'color': 'white',
            'border': '1px solid #0d6efd',
            'borderRadius': '4px',
            'fontWeight': '500',
            # Safari兼容性样式
            'WebkitAppearance': 'none',
            'WebkitTapHighlightColor': 'transparent',
            'userSelect': 'none',
            'cursor': 'pointer',
            'outline': 'none'
        }
        unselected_style = {
            'margin': '2px',
            'padding': '3px 8px',
            'fontSize': '12px',
            'color': '#3a3f45',  # 深灰色文字（调暗）
            'backgroundColor': '#d6d9dd',  # 浅灰色背景（调暗30%）
            'border': '1px solid #c4c8cc',  # 浅灰色边框（调暗30%）
            'borderRadius': '4px',
            # Safari兼容性样式
            'WebkitAppearance': 'none',
            'WebkitTapHighlightColor': 'transparent',
            'userSelect': 'none',
            'cursor': 'pointer',
            'outline': 'none'
        }
    else:
        # 深色主题样式（原有样式）
        selected_style = {
            'margin': '2px',
            'padding': '3px 8px',
            'fontSize': '12px',
            'backgroundColor': '#20c997',  # 更柔和的绿色背景表示选中
            'color': 'white',
            'border': '1px solid #20c997',
            'borderRadius': '4px',
            'fontWeight': '500',
            # Safari兼容性样式
            'WebkitAppearance': 'none',
            'WebkitTapHighlightColor': 'transparent',
            'userSelect': 'none',
            'cursor': 'pointer',
            'outline': 'none'
        }
        unselected_style = {
            'margin': '2px',
            'padding': '3px 8px',
            'fontSize': '12px',
            'color': '#f0f0f0',  # 灰色文字
            'backgroundColor': '#343a40',  # 更深的灰色背景
            'border': '1px solid #495057',  # 更深的边框
            'borderRadius': '4px',
            # Safari兼容性样式
            'WebkitAppearance': 'none',
            'WebkitTapHighlightColor': 'transparent',
            'userSelect': 'none',
            'cursor': 'pointer',
            'outline': 'none'
        }

    return [
        selected_style if interval['value'] == selected_interval else unselected_style
        for interval in time_intervals
    ]

# 修改现有的回调函数以更新选定的间隔
@app.callback(
    Output('selected-interval', 'data'),
    Input({'type': 'interval-btn', 'index': ALL}, 'n_clicks'),
    State('selected-interval', 'data')
)
def update_selected_interval(n_clicks, current_interval):
    ctx = dash.callback_context
    if not ctx.triggered:
        return current_interval
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    clicked_interval = json.loads(button_id)['index']
    return clicked_interval

# 使用客户端回调实现倒计时，支持动态刷新间隔（改进版）
app.clientside_callback(
    """
    function(n_intervals, update_interval, is_paused) {
        // 基础样式
        var base_style = {
            'display': 'inline-block',
            'marginLeft': '20px'
        };

        // 安全清除之前的倒计时
        try {
            if (window.countdownTimer) {
                clearInterval(window.countdownTimer);
                window.countdownTimer = null;
            }
        } catch (e) {
            console.warn('清除定时器时出错:', e);
        }

        // 如果暂停，只显示暂停状态，不启动倒计时
        if (is_paused) {
            var pause_style = Object.assign({}, base_style);
            pause_style['color'] = '#6c757d';  // 使用与项目主题一致的浅灰色
            return ['更新已暂停', pause_style];
        }

        // 计算更新间隔（秒）- 支持动态间隔
        var update_interval_seconds = Math.round(update_interval / 1000);
        var remaining_time = update_interval_seconds;

        // 启动新的倒计时（仅在未暂停时）
        try {
            window.countdownTimer = setInterval(function() {
                remaining_time--;
                if (remaining_time <= 0) {
                    remaining_time = update_interval_seconds;
                }

                // 更新倒计时显示，显示分钟和秒
                var countdown_element = document.getElementById('countdown-display');
                if (countdown_element) {
                    if (remaining_time >= 60) {
                        var minutes = Math.floor(remaining_time / 60);
                        var seconds = remaining_time % 60;
                        countdown_element.innerHTML = '⏰' + minutes + 'm' + seconds + 's';
                    } else {
                        countdown_element.innerHTML = '⏰' + remaining_time + 's';
                    }
                } else {
                    // 如果元素不存在，清除定时器
                    if (window.countdownTimer) {
                        clearInterval(window.countdownTimer);
                        window.countdownTimer = null;
                    }
                }
            }, 1000);
        } catch (e) {
            console.error('创建定时器时出错:', e);
            return ['⏰定时器错误', base_style];
        }

        // 返回初始显示
        if (update_interval_seconds >= 60) {
            var minutes = Math.floor(update_interval_seconds / 60);
            var seconds = update_interval_seconds % 60;
            return ['⏰' + minutes + 'm' + seconds + 's', base_style];
        } else {
            return ['⏰' + update_interval_seconds + 's', base_style];
        }
    }
    """,
    [Output('countdown-display', 'children'),
     Output('countdown-display', 'style')],
    [Input('interval-component', 'n_intervals'),
     Input('interval-component', 'interval'),
     Input('is-paused-store', 'data')]
)

# 动态调整刷新间隔：根据选择的时间周期计算合适的刷新间隔
@app.callback(
    Output('interval-component', 'interval'),
    [Input('selected-interval', 'data'),
     Input('pause-toggle-checklist', 'value')],
    prevent_initial_call=False
)
def update_refresh_interval(selected_interval, pause_checklist):
    # 检查是否暂停
    is_paused = 'paused' in pause_checklist if pause_checklist else False
    if is_paused:
        return 24 * 60 * 60 * 1000  # 24小时，实际上停止刷新

    # 根据时间周期动态计算刷新间隔
    # 基础公式：基础间隔 * (选择的时间周期 / 30秒)
    base_interval = REFRESH_INTERVAL_SECONDS
    multiplier = max(1, selected_interval / 30)  # 最小倍数为1
    dynamic_interval = int(base_interval * multiplier)

    print(f"动态刷新间隔计算: 基础{base_interval}秒 * 倍数{multiplier:.1f} = {dynamic_interval}秒")
    return dynamic_interval * 1000

# 更新暂停状态存储
@app.callback(
    Output('is-paused-store', 'data'),
    Input('pause-toggle-checklist', 'value'),
    prevent_initial_call=True
)
def update_pause_state(checklist_value):
    return 'paused' in checklist_value if checklist_value else False

# 更新控制栏和热力图容器样式的回调
@app.callback(
    [Output('control-bar', 'style'),
     Output('heatmap-container', 'style')],
    Input('theme-state', 'data'),
    prevent_initial_call=False
)
def update_container_styles(theme_state):
    if theme_state == 'light':
        # 浅色主题样式（调暗30%）
        control_bar_style = {
            'display': 'flex',
            'justifyContent': 'space-between',
            'alignItems': 'center',
            'margin': '5px 0',
            'padding': '6px 0',
            'borderBottom': '1px solid #c4c8cc',  # 浅色边框（调暗30%）
            'backgroundColor': '#d6d9dd'  # 浅色背景（调暗30%）
        }
        heatmap_container_style = {
            'backgroundColor': '#e8eaed',  # 浅色背景（调暗30%）
            'minHeight': '90vh'
        }
    else:
        # 深色主题样式
        control_bar_style = {
            'display': 'flex',
            'justifyContent': 'space-between',
            'alignItems': 'center',
            'margin': '5px 0',
            'padding': '6px 0',
            'borderBottom': '1px solid #444444',  # 深色边框
            'backgroundColor': '#2c2c2c'  # 深色背景
        }
        heatmap_container_style = {
            'backgroundColor': '#1e1e1e',  # 深色背景
            'minHeight': '90vh'
        }

    return control_bar_style, heatmap_container_style

# 添加页面初始化时的主题加载和清理机制
app.clientside_callback(
    """
    function(n_intervals) {
        // 只在第一次加载时执行
        if (n_intervals === 0) {
            // 页面卸载时清理定时器
            window.addEventListener('beforeunload', function() {
                try {
                    if (window.countdownTimer) {
                        clearInterval(window.countdownTimer);
                        window.countdownTimer = null;
                        console.log('页面卸载时清理定时器');
                    }
                } catch (e) {
                    console.warn('页面卸载清理时出错:', e);
                }
            });

            // 从localStorage读取保存的主题
            var savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.remove('light-theme');
                return 'dark';
            } else {
                // 默认使用深色主题（包括没有保存主题的情况）
                document.body.classList.remove('light-theme');
                return 'dark';
            }
        }
        return window.dash_clientside.no_update;
    }
    """,
    Output('theme-state', 'data', allow_duplicate=True),
    Input('interval-component', 'n_intervals'),
    prevent_initial_call='initial_duplicate'
)



def check_system_health():
    """检查系统健康状态"""
    print("\n=== 系统健康检查 ===")

    # 检查环境变量
    required_vars = ['INFLUXDB_URL', 'INFLUXDB_TOKEN', 'INFLUXDB_ORG', 'INFLUXDB_BUCKET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ 环境变量配置完整")

    # 检查InfluxDB连接
    influx_ok = False
    try:
        test_client = get_influx_client()
        if test_client:
            print("✅ InfluxDB连接正常")
            influx_ok = True
        else:
            print("❌ InfluxDB连接失败")
    except Exception as e:
        print(f"❌ InfluxDB连接异常: {e}")

    # 检查Redis连接
    redis_ok = False
    try:
        cache_stats = get_cache_stats()
        if cache_stats.get("status") == "connected":
            print("✅ Redis连接正常")
            print(f"   内存使用: {cache_stats.get('memory_used', 'N/A')}")
            print(f"   缓存键数: {cache_stats.get('total_keys', 0)}")
            redis_ok = True
        else:
            print(f"❌ Redis连接失败: {cache_stats.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ Redis连接异常: {e}")

    if not redis_ok:
        print("⚠️  Redis不可用，将使用降级模式（直接查询InfluxDB）")

    return influx_ok  # InfluxDB是必需的，Redis是可选的

if __name__ == '__main__':
    # 系统健康检查
    health_ok = check_system_health()

    if not health_ok:
        print("\n⚠️  系统健康检查未通过，但仍将启动服务器")
        print("请检查配置并确保InfluxDB服务正常运行")

    port = int(os.environ.get('PORT', 9786))
    host = os.environ.get('HOST', '0.0.0.0')
    print(f"\n🚀 服务器正在启动...")
    print(f"📍 访问地址: http://{host}:{port}/")
    print(f"🔄 刷新间隔: {REFRESH_INTERVAL_SECONDS}秒")
    print(f"⏱️  查询超时: {QUERY_TIMEOUT}秒")
    print(f"🔁 最大重试: {MAX_RETRY_ATTEMPTS}次")
    print("=" * 50)

    try:
        app.run(debug=False, host=host, port=port)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")


