#!/usr/bin/env python3
"""
测试Redis配置脚本
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_redis_config():
    """测试Redis配置"""
    print("🔧 Redis配置信息:")
    print(f"  主机: {os.getenv('REDIS_HOST', 'localhost')}")
    print(f"  端口: {os.getenv('REDIS_PORT', '6379')}")
    print(f"  数据库: {os.getenv('REDIS_DB', '0')}")
    print(f"  连接超时: {os.getenv('REDIS_SOCKET_TIMEOUT', '5')}秒")
    print(f"  连接超时: {os.getenv('REDIS_SOCKET_CONNECT_TIMEOUT', '5')}秒")
    print(f"  最大连接数: {os.getenv('REDIS_MAX_CONNECTIONS', '10')}")
    print(f"  重试超时: {os.getenv('REDIS_RETRY_ON_TIMEOUT', 'true')}")
    print(f"  健康检查间隔: {os.getenv('REDIS_HEALTH_CHECK_INTERVAL', '30')}秒")
    print(f"  基础缓存TTL: {os.getenv('REDIS_BASE_CACHE_TTL', '300')}秒")
    print(f"  增量缓存TTL: {os.getenv('REDIS_LATEST_CACHE_TTL', '10')}秒")
    
    redis_password = os.getenv('REDIS_PASSWORD')
    if redis_password:
        print(f"  密码: {'*' * len(redis_password)}")
    else:
        print("  密码: 未设置")

def test_redis_connection():
    """测试Redis连接"""
    print("\n🔍 测试Redis连接:")
    
    try:
        from redis_cache import get_redis_client, get_cache_stats
        
        client = get_redis_client()
        if client:
            print("✅ Redis连接成功")
            stats = get_cache_stats()
            print(f"  状态: {stats.get('status')}")
            print(f"  内存使用: {stats.get('memory_used', 'N/A')}")
            print(f"  缓存键数: {stats.get('total_keys', 0)}")
            return True
        else:
            print("❌ Redis连接失败")
            return False
            
    except Exception as e:
        print(f"❌ Redis连接异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Redis配置测试")
    print("=" * 40)
    
    # 测试配置读取
    test_redis_config()
    
    # 测试连接
    success = test_redis_connection()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 所有测试通过！Redis配置正常")
    else:
        print("⚠️ 测试失败，请检查Redis服务和配置")
    
    return success

if __name__ == "__main__":
    main()
