{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\", \"stdweb\", \"wasm-bindgen\"]", "target": 14160162848842265298, "profile": 15657897354478470176, "path": 8793921196160516399, "deps": [[8081351675046095464, "lock_api", false, 2748118666142226131], [14196108479452351812, "instant", false, 8215391684703788326], [14814334185036658946, "parking_lot_core", false, 17872356425886567867]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot-166d7cf0391564f8\\dep-lib-parking_lot", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}