{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 3482657298681496565, "deps": [[5103565458935487, "futures_io", false, 1360825392855313346], [1811549171721445101, "futures_channel", false, 11751067466842717899], [7013762810557009322, "futures_sink", false, 16030783060537813937], [7620660491849607393, "futures_core", false, 14401924481682515838], [10629569228670356391, "futures_util", false, 3972840536204483643], [12779779637805422465, "futures_executor", false, 14261947221829022713], [16240732885093539806, "futures_task", false, 7270017046372435839]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-882099a58d6e8756\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}