"""
Redis缓存管理模块
实现分层缓存策略：基础数据缓存 + 增量数据缓存
"""

import os
import redis
import pickle
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple
import threading
import time

# Redis连接锁
redis_lock = threading.RLock()
redis_client = None

def get_redis_client():
    """获取Redis客户端，支持自动重连"""
    global redis_client
    with redis_lock:
        try:
            if redis_client is None:
                print("初始化Redis客户端...")

                # 从环境变量读取Redis配置
                redis_config = {
                    'host': os.getenv('REDIS_HOST', 'localhost'),
                    'port': int(os.getenv('REDIS_PORT', 6379)),
                    'db': int(os.getenv('REDIS_DB', 0)),
                    'decode_responses': False,  # 用于存储pickle数据
                    'socket_timeout': int(os.getenv('REDIS_SOCKET_TIMEOUT', 5)),
                    'socket_connect_timeout': int(os.getenv('REDIS_SOCKET_CONNECT_TIMEOUT', 5)),
                    'retry_on_timeout': os.getenv('REDIS_RETRY_ON_TIMEOUT', 'true').lower() == 'true',
                    'health_check_interval': int(os.getenv('REDIS_HEALTH_CHECK_INTERVAL', 30)),
                    'max_connections': int(os.getenv('REDIS_MAX_CONNECTIONS', 10))
                }

                # 如果设置了密码，添加密码配置
                redis_password = os.getenv('REDIS_PASSWORD')
                if redis_password:
                    redis_config['password'] = redis_password

                print(f"Redis配置: {redis_config['host']}:{redis_config['port']}/{redis_config['db']}")
                redis_client = redis.Redis(**redis_config)
            
            # 简单的健康检查
            redis_client.ping()
            return redis_client
            
        except Exception as e:
            print(f"Redis连接异常，尝试重连: {e}")
            try:
                # 重连时使用相同的配置
                redis_config = {
                    'host': os.getenv('REDIS_HOST', 'localhost'),
                    'port': int(os.getenv('REDIS_PORT', 6379)),
                    'db': int(os.getenv('REDIS_DB', 0)),
                    'decode_responses': False,
                    'socket_timeout': int(os.getenv('REDIS_SOCKET_TIMEOUT', 5)),
                    'socket_connect_timeout': int(os.getenv('REDIS_SOCKET_CONNECT_TIMEOUT', 5)),
                    'retry_on_timeout': os.getenv('REDIS_RETRY_ON_TIMEOUT', 'true').lower() == 'true'
                }

                redis_password = os.getenv('REDIS_PASSWORD')
                if redis_password:
                    redis_config['password'] = redis_password

                redis_client = redis.Redis(**redis_config)
                redis_client.ping()
                print("Redis重连成功")
                return redis_client
            except Exception as reconnect_error:
                print(f"Redis重连失败: {reconnect_error}")
                redis_client = None
                return None

def get_base_data_cache(interval_seconds: int) -> Optional[pd.DataFrame]:
    """获取基础30分钟数据缓存"""
    try:
        client = get_redis_client()
        if client is None:
            return None
            
        cache_key = f"heatmap_base_30min_{interval_seconds}"
        cached_data = client.get(cache_key)
        if cached_data:
            df = pickle.loads(cached_data)
            print(f"✅ 基础缓存命中: {df.shape}")
            return df
    except Exception as e:
        print(f"获取基础缓存失败: {e}")
    return None

def set_base_data_cache(df: pd.DataFrame, interval_seconds: int, ttl: int = None):
    """缓存基础30分钟数据，TTL从环境变量读取，默认5分钟"""
    if ttl is None:
        ttl = int(os.getenv('REDIS_BASE_CACHE_TTL', 300))
    try:
        client = get_redis_client()
        if client is None:
            return False
            
        cache_key = f"heatmap_base_30min_{interval_seconds}"
        client.setex(cache_key, ttl, pickle.dumps(df))
        print(f"✅ 基础数据已缓存: {df.shape}, TTL={ttl}秒")
        return True
    except Exception as e:
        print(f"缓存基础数据失败: {e}")
        return False

def get_latest_data_cache(interval_seconds: int) -> Optional[pd.DataFrame]:
    """获取最新数据缓存"""
    try:
        client = get_redis_client()
        if client is None:
            return None
            
        cache_key = f"heatmap_latest_{interval_seconds}"
        cached_data = client.get(cache_key)
        if cached_data:
            df = pickle.loads(cached_data)
            print(f"✅ 增量缓存命中: {df.shape}")
            return df
    except Exception as e:
        print(f"获取增量缓存失败: {e}")
    return None

def set_latest_data_cache(df: pd.DataFrame, interval_seconds: int, ttl: int = None):
    """缓存最新数据，TTL从环境变量读取，默认10秒"""
    if ttl is None:
        ttl = int(os.getenv('REDIS_LATEST_CACHE_TTL', 10))
    try:
        client = get_redis_client()
        if client is None:
            return False
            
        cache_key = f"heatmap_latest_{interval_seconds}"
        client.setex(cache_key, ttl, pickle.dumps(df))
        print(f"✅ 增量数据已缓存: {df.shape}, TTL={ttl}秒")
        return True
    except Exception as e:
        print(f"缓存增量数据失败: {e}")
        return False

def get_cache_stats() -> dict:
    """获取缓存统计信息"""
    try:
        client = get_redis_client()
        if client is None:
            return {"status": "disconnected"}
            
        info = client.info()
        keys = client.keys("heatmap_*")
        
        return {
            "status": "connected",
            "memory_used": info.get('used_memory_human', 'N/A'),
            "total_keys": len(keys),
            "cache_keys": [key.decode() if isinstance(key, bytes) else key for key in keys],
            "connected_clients": info.get('connected_clients', 0)
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}

def clear_cache(pattern: str = "heatmap_*"):
    """清理缓存"""
    try:
        client = get_redis_client()
        if client is None:
            return False
            
        keys = client.keys(pattern)
        if keys:
            client.delete(*keys)
            print(f"✅ 已清理 {len(keys)} 个缓存键")
            return True
        else:
            print("没有找到匹配的缓存键")
            return True
    except Exception as e:
        print(f"清理缓存失败: {e}")
        return False

def combine_cached_data(base_df: pd.DataFrame, latest_df: pd.DataFrame) -> pd.DataFrame:
    """合并基础数据和增量数据"""
    try:
        if base_df.empty and latest_df.empty:
            return pd.DataFrame()
        elif base_df.empty:
            return latest_df.copy()
        elif latest_df.empty:
            return base_df.copy()
        
        # 合并数据，保留最新的记录
        combined_df = pd.concat([base_df, latest_df], ignore_index=True)
        
        # 去重，保留最新的记录（按时间和symbol）
        combined_df = combined_df.drop_duplicates(
            subset=['time', 'symbol'], 
            keep='last'
        ).sort_values(['symbol', 'time'])
        
        print(f"✅ 数据合并完成: 基础{base_df.shape} + 增量{latest_df.shape} = 合并{combined_df.shape}")
        return combined_df
        
    except Exception as e:
        print(f"合并数据失败: {e}")
        return base_df if not base_df.empty else latest_df

def should_refresh_base_cache(interval_seconds: int) -> bool:
    """判断是否需要刷新基础缓存"""
    try:
        client = get_redis_client()
        if client is None:
            return True
            
        cache_key = f"heatmap_base_30min_{interval_seconds}"
        ttl = client.ttl(cache_key)
        
        # 如果TTL小于60秒或者键不存在，则需要刷新
        return ttl < 60 or ttl == -2
        
    except Exception as e:
        print(f"检查基础缓存TTL失败: {e}")
        return True

# 初始化Redis客户端
try:
    redis_client = get_redis_client()
    if redis_client:
        print("✅ Redis客户端初始化成功")
    else:
        print("⚠️ Redis客户端初始化失败，将使用降级模式")
except Exception as e:
    print(f"⚠️ Redis客户端初始化异常: {e}")
    redis_client = None
