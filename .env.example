# InfluxDB配置示例
# 连接Docker中的InfluxDB的配置，采用本地或网络连接数据库地址⭐️
INFLUXDB_URL=http://localhost:8086
# INFLUXDB_URL=http://***********:8086  # 远程InfluxDB服务器
INFLUXDB_ORG=myorg
INFLUXDB_BUCKET=mybucket
INFLUXDB_TOKEN=mytoken
TZ=UTC

# 机器集群配置⭐️
MACHINE_ID=1
CYCLE_INTERVAL_SECONDS=30
STARTUP_DELAY_PER_MACHINE=6

# API接口配置
GET_SYMBOLS_URL=http://*************:8888/api/v1/binance/perps/symbols/trading

# 通知配置
BARK_SERVER_URL=http://**************:8580/FhQbH8Fo7veS6cVAnbL8Mm/
LOG_LEVEL=INFO
BARK_SYMBOLS=CAKEUSDT, FORMUSDT, NEIROETHUSDT, ETHUSDT

# 热力图刷新间隔设置（秒）
REFRESH_INTERVAL_SECONDS=8

# Redis缓存配置⭐️
# ==========================================
# 基础连接配置
# ==========================================

# 本地Redis（Docker Compose）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# 远程Redis服务器示例
# REDIS_HOST=***********
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=your_redis_password

# Docker Compose中的Redis服务
# REDIS_HOST=redis
# REDIS_PORT=6379
# REDIS_DB=0

# ==========================================
# 连接参数配置
# ==========================================

# 连接超时设置（秒）
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# 连接池配置
REDIS_MAX_CONNECTIONS=10
REDIS_RETRY_ON_TIMEOUT=true

# 健康检查间隔（秒）
REDIS_HEALTH_CHECK_INTERVAL=30

# ==========================================
# 缓存策略配置
# ==========================================

# 基础数据缓存时间（30分钟历史数据，秒）
# 推荐值：300秒（5分钟）
REDIS_BASE_CACHE_TTL=300

# 增量数据缓存时间（最新2-3分钟数据，秒）
# 推荐值：10秒
REDIS_LATEST_CACHE_TTL=10

# ==========================================
# 不同环境配置示例
# ==========================================

# 开发环境（本地Redis）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_BASE_CACHE_TTL=60    # 较短的缓存时间便于测试
# REDIS_LATEST_CACHE_TTL=5

# 生产环境（远程Redis集群）
# REDIS_HOST=redis-cluster.example.com
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=production_password
# REDIS_BASE_CACHE_TTL=600   # 较长的缓存时间提高性能
# REDIS_LATEST_CACHE_TTL=15

# 测试环境（内存Redis）
# REDIS_HOST=localhost
# REDIS_PORT=6380
# REDIS_DB=1
# REDIS_BASE_CACHE_TTL=30    # 很短的缓存时间
# REDIS_LATEST_CACHE_TTL=3
