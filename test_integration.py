#!/usr/bin/env python3
"""
集成测试脚本 - 测试Redis缓存集成到主应用的效果
"""

import os
import sys
import time
from datetime import datetime, timezone
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试Redis缓存模块导入
        from redis_cache import (
            get_redis_client, get_cache_stats, 
            get_base_data_cache, set_base_data_cache
        )
        print("✅ Redis缓存模块导入成功")
        
        # 测试主应用模块导入
        from influx_query_dash_heatmap_FF import (
            query_data, query_data_optimized, query_data_fallback
        )
        print("✅ 主应用模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入异常: {e}")
        return False

def test_fallback_mode():
    """测试降级模式（Redis不可用时）"""
    print("\n=== 测试降级模式 ===")
    
    try:
        from influx_query_dash_heatmap_FF import query_data_fallback
        
        print("测试原始查询函数...")
        start_time = time.time()
        
        # 使用较短的间隔进行测试
        result = query_data_fallback(30)
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"查询耗时: {query_time:.3f}秒")
        print(f"数据量: {len(result) if not result.empty else 0} 条记录")
        
        if not result.empty:
            print("✅ 降级模式查询成功")
            print(f"数据形状: {result.shape}")
            print("数据列:", list(result.columns))
            return True
        else:
            print("⚠️ 查询返回空数据（可能是InfluxDB连接问题）")
            return False
            
    except Exception as e:
        print(f"❌ 降级模式测试失败: {e}")
        return False

def test_optimized_query():
    """测试优化查询（会自动降级到fallback）"""
    print("\n=== 测试优化查询 ===")
    
    try:
        from influx_query_dash_heatmap_FF import query_data
        
        print("测试优化查询函数（预期会降级到原始查询）...")
        start_time = time.time()
        
        result = query_data(30)
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"查询耗时: {query_time:.3f}秒")
        print(f"数据量: {len(result) if not result.empty else 0} 条记录")
        
        if not result.empty:
            print("✅ 优化查询成功（降级模式）")
            return True
        else:
            print("⚠️ 查询返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ 优化查询测试失败: {e}")
        return False

def test_environment_config():
    """测试环境配置"""
    print("\n=== 测试环境配置 ===")
    
    # 检查必需的环境变量
    required_vars = [
        'INFLUXDB_URL', 'INFLUXDB_TOKEN', 'INFLUXDB_ORG', 'INFLUXDB_BUCKET'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:20]}..." if len(value) > 20 else f"✅ {var}: {value}")
        else:
            missing_vars.append(var)
            print(f"❌ {var}: 未设置")
    
    # 检查Redis配置
    redis_vars = ['REDIS_HOST', 'REDIS_PORT', 'REDIS_DB']
    for var in redis_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚠️ {var}: 使用默认值")
    
    if missing_vars:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ 环境配置检查通过")
        return True

def test_system_health():
    """测试系统健康检查"""
    print("\n=== 测试系统健康检查 ===")
    
    try:
        from influx_query_dash_heatmap_FF import check_system_health
        
        print("执行系统健康检查...")
        health_ok = check_system_health()
        
        if health_ok:
            print("✅ 系统健康检查通过")
            return True
        else:
            print("⚠️ 系统健康检查未完全通过（但可能仍可运行）")
            return True  # 即使Redis不可用，InfluxDB可用就算通过
            
    except Exception as e:
        print(f"❌ 系统健康检查失败: {e}")
        return False

def test_app_startup():
    """测试应用启动准备"""
    print("\n=== 测试应用启动准备 ===")
    
    try:
        # 检查Dash应用是否可以初始化
        from influx_query_dash_heatmap_FF import app
        
        if app:
            print("✅ Dash应用初始化成功")
            print(f"应用标题: {app.title}")
            return True
        else:
            print("❌ Dash应用初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 应用启动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Redis缓存集成测试开始")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("环境配置", test_environment_config()))
    test_results.append(("系统健康检查", test_system_health()))
    test_results.append(("降级模式", test_fallback_mode()))
    test_results.append(("优化查询", test_optimized_query()))
    test_results.append(("应用启动", test_app_startup()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 集成测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed >= total - 1:  # 允许一个测试失败
        print("🎉 集成测试基本通过！应用可以启动")
        print("\n📝 下一步操作:")
        print("1. 启动Docker Desktop")
        print("2. 运行: docker-compose up -d")
        print("3. 运行: python test_redis.py")
        print("4. 运行: python influx_query_dash_heatmap_FF.py")
        return True
    else:
        print("⚠️ 集成测试失败较多，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
