#!/usr/bin/env python3
"""
性能对比测试脚本
对比使用Redis缓存前后的查询性能
"""

import os
import sys
import time
import statistics
from datetime import datetime, timezone
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入必要模块
try:
    from redis_cache import get_cache_stats, clear_cache
    print("✅ Redis缓存模块导入成功")
except ImportError as e:
    print(f"❌ Redis缓存模块导入失败: {e}")
    sys.exit(1)

def test_query_performance(use_cache=True, iterations=5):
    """测试查询性能"""
    print(f"\n=== 测试查询性能 ({'使用缓存' if use_cache else '不使用缓存'}) ===")
    
    try:
        # 导入查询函数
        if use_cache:
            from influx_query_dash_heatmap_FF import query_data
        else:
            from influx_query_dash_heatmap_FF import query_data_fallback as query_data
        
        times = []
        interval_seconds = 30
        
        for i in range(iterations):
            print(f"执行第 {i+1}/{iterations} 次查询...")
            
            start_time = time.time()
            result = query_data(interval_seconds)
            end_time = time.time()
            
            query_time = end_time - start_time
            times.append(query_time)
            
            print(f"  查询耗时: {query_time:.3f}秒, 数据量: {len(result) if not result.empty else 0}")
            
            # 避免过于频繁的查询
            if i < iterations - 1:
                time.sleep(2)
        
        # 计算统计信息
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\n📊 性能统计:")
        print(f"  平均耗时: {avg_time:.3f}秒")
        print(f"  最短耗时: {min_time:.3f}秒")
        print(f"  最长耗时: {max_time:.3f}秒")
        
        return {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'times': times
        }
        
    except Exception as e:
        print(f"❌ 查询性能测试失败: {e}")
        return None

def compare_performance():
    """对比缓存前后的性能"""
    print("\n🏁 开始性能对比测试")
    print("=" * 60)
    
    # 清理缓存，确保公平对比
    print("清理现有缓存...")
    clear_cache()
    time.sleep(1)
    
    # 测试不使用缓存的性能
    print("\n🐌 测试原始查询性能（不使用缓存）")
    fallback_results = test_query_performance(use_cache=False, iterations=3)
    
    if fallback_results is None:
        print("❌ 原始查询测试失败")
        return False
    
    # 等待一段时间
    time.sleep(3)
    
    # 测试使用缓存的性能
    print("\n🚀 测试缓存查询性能（使用Redis缓存）")
    
    # 第一次查询（预热缓存）
    print("预热缓存...")
    from influx_query_dash_heatmap_FF import query_data
    query_data(30)
    time.sleep(1)
    
    # 正式测试缓存性能
    cache_results = test_query_performance(use_cache=True, iterations=5)
    
    if cache_results is None:
        print("❌ 缓存查询测试失败")
        return False
    
    # 计算性能提升
    print("\n" + "=" * 60)
    print("📈 性能对比结果:")
    
    fallback_avg = fallback_results['avg_time']
    cache_avg = cache_results['avg_time']
    
    if cache_avg > 0:
        improvement = fallback_avg / cache_avg
        time_saved = fallback_avg - cache_avg
        
        print(f"原始查询平均耗时: {fallback_avg:.3f}秒")
        print(f"缓存查询平均耗时: {cache_avg:.3f}秒")
        print(f"性能提升倍数: {improvement:.1f}倍")
        print(f"每次查询节省: {time_saved:.3f}秒")
        
        # 计算每小时节省的时间
        queries_per_hour = 3600 / 8  # 假设8秒刷新一次
        time_saved_per_hour = time_saved * queries_per_hour
        
        print(f"每小时节省时间: {time_saved_per_hour:.1f}秒")
        
        # 评估性能提升效果
        if improvement >= 10:
            print("🎉 性能提升显著！")
        elif improvement >= 3:
            print("✅ 性能提升明显")
        elif improvement >= 1.5:
            print("👍 性能有所提升")
        else:
            print("⚠️ 性能提升不明显")
        
        return True
    else:
        print("❌ 无法计算性能提升（缓存查询时间为0）")
        return False

def test_cache_hit_rate():
    """测试缓存命中率"""
    print("\n=== 测试缓存命中率 ===")
    
    try:
        from influx_query_dash_heatmap_FF import query_data
        
        # 清理缓存
        clear_cache()
        
        # 第一次查询（缓存未命中）
        print("第一次查询（预期缓存未命中）...")
        start_time = time.time()
        query_data(30)
        first_query_time = time.time() - start_time
        print(f"首次查询耗时: {first_query_time:.3f}秒")
        
        # 等待一秒
        time.sleep(1)
        
        # 第二次查询（预期缓存命中）
        print("第二次查询（预期缓存命中）...")
        start_time = time.time()
        query_data(30)
        second_query_time = time.time() - start_time
        print(f"缓存查询耗时: {second_query_time:.3f}秒")
        
        # 计算缓存效果
        if second_query_time > 0:
            cache_speedup = first_query_time / second_query_time
            print(f"缓存加速比: {cache_speedup:.1f}倍")
            
            if cache_speedup >= 5:
                print("✅ 缓存效果优秀")
                return True
            elif cache_speedup >= 2:
                print("👍 缓存效果良好")
                return True
            else:
                print("⚠️ 缓存效果一般")
                return False
        else:
            print("❌ 无法计算缓存效果")
            return False
            
    except Exception as e:
        print(f"❌ 缓存命中率测试失败: {e}")
        return False

def test_concurrent_access():
    """测试并发访问性能"""
    print("\n=== 测试并发访问性能 ===")
    
    try:
        import threading
        from influx_query_dash_heatmap_FF import query_data
        
        # 清理缓存
        clear_cache()
        
        # 预热缓存
        print("预热缓存...")
        query_data(30)
        time.sleep(1)
        
        # 并发查询测试
        results = []
        threads = []
        
        def concurrent_query(thread_id):
            start_time = time.time()
            result = query_data(30)
            end_time = time.time()
            results.append({
                'thread_id': thread_id,
                'time': end_time - start_time,
                'data_size': len(result) if not result.empty else 0
            })
        
        # 启动5个并发线程
        print("启动5个并发查询...")
        for i in range(5):
            thread = threading.Thread(target=concurrent_query, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        if results:
            times = [r['time'] for r in results]
            avg_time = statistics.mean(times)
            max_time = max(times)
            min_time = min(times)
            
            print(f"并发查询结果:")
            for r in results:
                print(f"  线程{r['thread_id']}: {r['time']:.3f}秒, 数据量: {r['data_size']}")
            
            print(f"\n并发性能统计:")
            print(f"  平均耗时: {avg_time:.3f}秒")
            print(f"  最短耗时: {min_time:.3f}秒")
            print(f"  最长耗时: {max_time:.3f}秒")
            
            if max_time < 0.1:
                print("🚀 并发性能优秀")
                return True
            elif max_time < 0.5:
                print("✅ 并发性能良好")
                return True
            else:
                print("⚠️ 并发性能一般")
                return False
        else:
            print("❌ 没有获取到并发查询结果")
            return False
            
    except Exception as e:
        print(f"❌ 并发访问测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🏃‍♂️ Redis缓存性能测试开始")
    print("=" * 60)
    
    # 检查Redis连接
    stats = get_cache_stats()
    if stats.get('status') != 'connected':
        print("❌ Redis未连接，无法进行性能测试")
        return False
    
    print(f"✅ Redis连接正常，内存使用: {stats.get('memory_used', 'N/A')}")
    
    test_results = []
    
    # 执行性能测试
    test_results.append(("性能对比", compare_performance()))
    test_results.append(("缓存命中率", test_cache_hit_rate()))
    test_results.append(("并发访问", test_concurrent_access()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 性能测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 性能测试全部通过！Redis缓存优化效果显著")
        return True
    else:
        print("⚠️ 部分性能测试失败，建议检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
