{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 11030844300562395373, "deps": [[5103565458935487, "futures_io", false, 1360825392855313346], [1615478164327904835, "pin_utils", false, 2861129800015584206], [1811549171721445101, "futures_channel", false, 11751067466842717899], [1906322745568073236, "pin_project_lite", false, 17143027058502063303], [5451793922601807560, "slab", false, 4160224633911992789], [7013762810557009322, "futures_sink", false, 16030783060537813937], [7620660491849607393, "futures_core", false, 14401924481682515838], [10565019901765856648, "futures_macro", false, 6625554149147108408], [15932120279885307830, "memchr", false, 9354090706820272253], [16240732885093539806, "futures_task", false, 7270017046372435839]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-74b5bf01293dba1b\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}