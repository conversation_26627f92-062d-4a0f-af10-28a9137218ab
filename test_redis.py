#!/usr/bin/env python3
"""
Redis连接和缓存功能测试脚本
"""

import os
import sys
import time
import pandas as pd
from datetime import datetime, timezone
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入Redis缓存模块
try:
    from redis_cache import (
        get_redis_client, get_cache_stats, clear_cache,
        get_base_data_cache, set_base_data_cache,
        get_latest_data_cache, set_latest_data_cache,
        combine_cached_data
    )
    print("✅ Redis缓存模块导入成功")
except ImportError as e:
    print(f"❌ Redis缓存模块导入失败: {e}")
    sys.exit(1)

def test_redis_connection():
    """测试Redis基本连接"""
    print("\n=== 测试Redis连接 ===")
    
    try:
        client = get_redis_client()
        if client is None:
            print("❌ Redis客户端获取失败")
            return False
        
        # 测试ping
        client.ping()
        print("✅ Redis连接成功")
        
        # 测试基本读写
        test_key = "test_connection"
        test_value = "test_value_" + str(int(time.time()))
        
        client.set(test_key, test_value, ex=10)
        retrieved_value = client.get(test_key)
        
        if retrieved_value and retrieved_value.decode() == test_value:
            print("✅ Redis读写测试成功")
            client.delete(test_key)
            return True
        else:
            print("❌ Redis读写测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Redis连接测试失败: {e}")
        return False

def test_cache_operations():
    """测试缓存操作"""
    print("\n=== 测试缓存操作 ===")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'time': [datetime.now(timezone.utc)] * 3,
            'symbol': ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
            'pct_change': [1.5, -0.8, 2.1]
        })
        
        interval_seconds = 30
        
        # 测试基础数据缓存
        print("测试基础数据缓存...")
        success = set_base_data_cache(test_data, interval_seconds, ttl=60)
        if success:
            print("✅ 基础数据缓存设置成功")
        else:
            print("❌ 基础数据缓存设置失败")
            return False
        
        # 测试基础数据读取
        cached_base = get_base_data_cache(interval_seconds)
        if cached_base is not None and len(cached_base) == len(test_data):
            print("✅ 基础数据缓存读取成功")
        else:
            print("❌ 基础数据缓存读取失败")
            return False
        
        # 测试增量数据缓存
        print("测试增量数据缓存...")
        latest_data = test_data.copy()
        latest_data['pct_change'] = [2.0, -1.2, 1.8]
        
        success = set_latest_data_cache(latest_data, interval_seconds, ttl=10)
        if success:
            print("✅ 增量数据缓存设置成功")
        else:
            print("❌ 增量数据缓存设置失败")
            return False
        
        # 测试增量数据读取
        cached_latest = get_latest_data_cache(interval_seconds)
        if cached_latest is not None and len(cached_latest) == len(latest_data):
            print("✅ 增量数据缓存读取成功")
        else:
            print("❌ 增量数据缓存读取失败")
            return False
        
        # 测试数据合并
        print("测试数据合并...")
        combined = combine_cached_data(cached_base, cached_latest)
        if not combined.empty and len(combined) >= len(test_data):
            print("✅ 数据合并成功")
        else:
            print("❌ 数据合并失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存操作测试失败: {e}")
        return False

def test_cache_stats():
    """测试缓存统计"""
    print("\n=== 测试缓存统计 ===")
    
    try:
        stats = get_cache_stats()
        print(f"缓存状态: {stats.get('status', 'unknown')}")
        print(f"内存使用: {stats.get('memory_used', 'N/A')}")
        print(f"缓存键数: {stats.get('total_keys', 0)}")
        print(f"连接客户端: {stats.get('connected_clients', 0)}")
        
        if stats.get('cache_keys'):
            print("缓存键列表:")
            for key in stats['cache_keys']:
                print(f"  - {key}")
        
        return stats.get('status') == 'connected'
        
    except Exception as e:
        print(f"❌ 缓存统计测试失败: {e}")
        return False

def test_performance():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    try:
        # 创建较大的测试数据集
        large_data = pd.DataFrame({
            'time': [datetime.now(timezone.utc)] * 1000,
            'symbol': [f'SYMBOL{i}USDT' for i in range(1000)],
            'pct_change': [i * 0.1 for i in range(1000)]
        })
        
        interval_seconds = 60
        
        # 测试写入性能
        start_time = time.time()
        set_base_data_cache(large_data, interval_seconds, ttl=120)
        write_time = time.time() - start_time
        print(f"✅ 大数据集缓存写入耗时: {write_time:.3f}秒")
        
        # 测试读取性能
        start_time = time.time()
        cached_data = get_base_data_cache(interval_seconds)
        read_time = time.time() - start_time
        print(f"✅ 大数据集缓存读取耗时: {read_time:.3f}秒")
        
        if cached_data is not None and len(cached_data) == len(large_data):
            print(f"✅ 数据完整性验证通过: {len(cached_data)} 条记录")
            
            # 计算性能提升
            if read_time < 0.1:  # 假设原始查询需要1秒
                improvement = 1.0 / read_time if read_time > 0 else float('inf')
                print(f"🚀 预估性能提升: {improvement:.1f}倍")
            
            return True
        else:
            print("❌ 数据完整性验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    try:
        success = clear_cache("heatmap_*")
        if success:
            print("✅ 测试数据清理完成")
        else:
            print("⚠️ 测试数据清理可能不完整")
        return success
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Redis缓存功能测试开始")
    print("=" * 50)
    
    # 显示配置信息
    print(f"Redis主机: {os.getenv('REDIS_HOST', 'localhost')}")
    print(f"Redis端口: {os.getenv('REDIS_PORT', 6379)}")
    print(f"Redis数据库: {os.getenv('REDIS_DB', 0)}")
    
    test_results = []
    
    # 执行测试
    test_results.append(("Redis连接", test_redis_connection()))
    test_results.append(("缓存操作", test_cache_operations()))
    test_results.append(("缓存统计", test_cache_stats()))
    test_results.append(("性能测试", test_performance()))
    
    # 清理测试数据
    cleanup_test_data()
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Redis缓存功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查Redis配置和连接")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
