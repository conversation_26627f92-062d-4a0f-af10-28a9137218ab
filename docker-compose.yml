services:
  influxdb:
    image: influxdb:2.6
    container_name: bnu_openinterest_influxdb
    restart: always
    ports:
      - "8086:8086"
    volumes:
      - influxdb-data:/var/lib/influxdb2
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=myuser
      - DOCKER_INFLUXDB_INIT_PASSWORD=mypassword
      - DOCKER_INFLUXDB_INIT_ORG=myorg
      - DOCKER_INFLUXDB_INIT_BUCKET=mybucket
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=mytoken

  redis:
    image: redis:7-alpine
    container_name: bnu_openinterest_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

volumes:
  influxdb-data:
  redis-data: