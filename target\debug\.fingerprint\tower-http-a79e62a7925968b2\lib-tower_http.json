{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 14264657120815013845, "deps": [[784494742817713399, "tower_service", false, 18402585402976662615], [1906322745568073236, "pin_project_lite", false, 17143027058502063303], [4121350475192885151, "iri_string", false, 8736981823458940697], [5695049318159433696, "tower", false, 11206590146151755474], [7712452662827335977, "tower_layer", false, 1104044362327426186], [7896293946984509699, "bitflags", false, 10787384037786657317], [9010263965687315507, "http", false, 3736580640712029992], [10629569228670356391, "futures_util", false, 3972840536204483643], [14084095096285906100, "http_body", false, 1581608618866217959], [16066129441945555748, "bytes", false, 7899407690149537674]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-a79e62a7925968b2\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}