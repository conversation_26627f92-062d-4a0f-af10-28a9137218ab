#!/usr/bin/env python3
"""
检查Redis缓存状态脚本
"""

import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

try:
    from redis_cache import get_cache_stats, get_redis_client
    print("✅ Redis缓存模块导入成功")
except ImportError as e:
    print(f"❌ Redis缓存模块导入失败: {e}")
    exit(1)

def check_cache_status():
    """检查缓存状态"""
    print("🔍 检查Redis缓存状态")
    print("=" * 40)
    
    try:
        stats = get_cache_stats()
        
        print(f"连接状态: {stats.get('status', 'unknown')}")
        print(f"内存使用: {stats.get('memory_used', 'N/A')}")
        print(f"缓存键数: {stats.get('total_keys', 0)}")
        print(f"连接客户端: {stats.get('connected_clients', 0)}")
        
        if stats.get('cache_keys'):
            print(f"\n📋 缓存键列表 ({len(stats['cache_keys'])} 个):")
            for key in stats['cache_keys']:
                print(f"  🔑 {key}")
        else:
            print("\n📋 当前没有缓存键")
        
        # 检查具体的缓存内容
        client = get_redis_client()
        if client:
            print(f"\n📊 Redis详细信息:")
            info = client.info()
            print(f"  已用内存: {info.get('used_memory_human', 'N/A')}")
            print(f"  峰值内存: {info.get('used_memory_peak_human', 'N/A')}")
            print(f"  键空间命中率: {info.get('keyspace_hits', 0)}")
            print(f"  键空间未命中: {info.get('keyspace_misses', 0)}")
            
            # 计算命中率
            hits = info.get('keyspace_hits', 0)
            misses = info.get('keyspace_misses', 0)
            if hits + misses > 0:
                hit_rate = hits / (hits + misses) * 100
                print(f"  缓存命中率: {hit_rate:.1f}%")
            else:
                print(f"  缓存命中率: N/A (还没有查询)")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查缓存状态失败: {e}")
        return False

def monitor_cache(duration=30):
    """监控缓存状态变化"""
    print(f"\n🔄 监控缓存状态变化 ({duration}秒)")
    print("=" * 40)
    
    start_time = time.time()
    last_keys = set()
    
    while time.time() - start_time < duration:
        try:
            stats = get_cache_stats()
            current_keys = set(stats.get('cache_keys', []))
            
            # 检查新增的键
            new_keys = current_keys - last_keys
            if new_keys:
                print(f"🆕 新增缓存键: {', '.join(new_keys)}")
            
            # 检查删除的键
            removed_keys = last_keys - current_keys
            if removed_keys:
                print(f"🗑️ 删除缓存键: {', '.join(removed_keys)}")
            
            last_keys = current_keys
            
            print(f"⏰ {time.strftime('%H:%M:%S')} - 缓存键数: {len(current_keys)}, 内存: {stats.get('memory_used', 'N/A')}")
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")
            break
        except Exception as e:
            print(f"❌ 监控错误: {e}")
            time.sleep(5)

def main():
    """主函数"""
    print("🔍 Redis缓存状态检查工具")
    print("=" * 50)
    
    # 检查当前状态
    if not check_cache_status():
        return False
    
    # 询问是否要监控
    print(f"\n💡 提示: 应用正在运行时，缓存会自动更新")
    print(f"   你可以在浏览器中访问 http://127.0.0.1:9786 来触发缓存")
    
    try:
        choice = input("\n是否要监控缓存状态变化？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            monitor_cache(60)  # 监控60秒
    except KeyboardInterrupt:
        print("\n👋 再见！")
    
    return True

if __name__ == "__main__":
    main()
